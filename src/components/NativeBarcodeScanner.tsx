'use client'

import { useEffect, useRef, useState, useCallback } from 'react'

interface NativeBarcodeScannerProps {
  onScan: (code: string) => void
  onError?: (error: string) => void
}

export default function NativeBarcodeScanner({ onScan, onError }: NativeBarcodeScannerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const streamRef = useRef<MediaStream | null>(null)
  const animationRef = useRef<number | null>(null)
  const [isScanning, setIsScanning] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hasDetector, setHasDetector] = useState(false)

  // Check for BarcodeDetector support
  useEffect(() => {
    setHasDetector('BarcodeDetector' in window)
  }, [])

  // Cleanup function
  const cleanup = useCallback(() => {
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current)
      animationRef.current = null
    }
    
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop())
      streamRef.current = null
    }
    
    setIsScanning(false)
  }, [])

  // Start camera
  const startCamera = useCallback(async () => {
    try {
      console.log('📱 Starting native camera scanner...')
      setError(null)

      // Try multiple camera configurations for maximum compatibility
      const constraints = [
        // High quality back camera
        {
          video: {
            facingMode: 'environment',
            width: { ideal: 1920, max: 1920 },
            height: { ideal: 1080, max: 1080 }
          }
        },
        // Medium quality back camera
        {
          video: {
            facingMode: 'environment',
            width: { ideal: 1280 },
            height: { ideal: 720 }
          }
        },
        // Basic back camera
        {
          video: {
            facingMode: 'environment'
          }
        },
        // Any camera
        {
          video: true
        }
      ]

      let stream = null
      for (const constraint of constraints) {
        try {
          console.log('🎥 Trying camera constraint:', constraint)
          stream = await navigator.mediaDevices.getUserMedia(constraint)
          console.log('✅ Camera started with constraint')
          break
        } catch (err) {
          console.log('⚠️ Camera constraint failed:', err)
          continue
        }
      }

      if (!stream) {
        throw new Error('Could not access any camera configuration')
      }

      streamRef.current = stream

      if (videoRef.current) {
        videoRef.current.srcObject = stream
        
        // Handle video events
        videoRef.current.onloadedmetadata = () => {
          console.log('📹 Video metadata loaded')
          if (videoRef.current) {
            videoRef.current.play()
              .then(() => {
                console.log('▶️ Video playing')
                setIsScanning(true)
                startDetection()
              })
              .catch(err => {
                console.error('❌ Video play failed:', err)
                setError('Video playback failed')
              })
          }
        }

        videoRef.current.onerror = (err) => {
          console.error('❌ Video error:', err)
          setError('Video error occurred')
        }
      }

    } catch (error) {
      console.error('❌ Camera access failed:', error)
      const errorMessage = error instanceof Error ? error.message : 'Camera access failed'
      setError(errorMessage)
      onError?.(errorMessage)
    }
  }, [onError])

  // Barcode detection loop
  const startDetection = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return

    const detectFrame = () => {
      if (!videoRef.current || !canvasRef.current || !streamRef.current) return

      const video = videoRef.current
      const canvas = canvasRef.current
      const context = canvas.getContext('2d')

      if (!context || video.videoWidth === 0 || video.videoHeight === 0) {
        animationRef.current = requestAnimationFrame(detectFrame)
        return
      }

      // Set canvas size to match video
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      // Draw current video frame
      context.drawImage(video, 0, 0, canvas.width, canvas.height)

      // Try barcode detection
      if (hasDetector) {
        try {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const detector = new (window as any).BarcodeDetector()
          detector.detect(canvas)
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            .then((barcodes: any[]) => {
              if (barcodes.length > 0) {
                const barcode = barcodes[0]
                console.log('🎉 Barcode detected:', barcode.rawValue)
                onScan(barcode.rawValue)
                
                // Brief pause after successful scan
                setTimeout(() => {
                  if (streamRef.current) {
                    animationRef.current = requestAnimationFrame(detectFrame)
                  }
                }, 1500)
                return
              }
              
              // Continue scanning
              if (streamRef.current) {
                animationRef.current = requestAnimationFrame(detectFrame)
              }
            })
            .catch(() => {
              // Continue scanning even if detection fails
              if (streamRef.current) {
                animationRef.current = requestAnimationFrame(detectFrame)
              }
            })
        } catch {
          // Fallback: just continue the loop
          if (streamRef.current) {
            animationRef.current = requestAnimationFrame(detectFrame)
          }
        }
      } else {
        // No barcode detector - just continue the loop for manual scanning
        if (streamRef.current) {
          animationRef.current = requestAnimationFrame(detectFrame)
        }
      }
    }

    animationRef.current = requestAnimationFrame(detectFrame)
  }, [hasDetector, onScan])

  // Auto-start on mount
  useEffect(() => {
    const init = async () => {
      // Small delay to ensure component is mounted
      await new Promise(resolve => setTimeout(resolve, 100))
      await startCamera()
    }

    init()
    return cleanup
  }, [startCamera, cleanup, startDetection])

  return (
    <div className="w-full max-w-2xl mx-auto">
      {/* Camera Preview */}
      <div className="relative bg-black rounded-lg overflow-hidden" style={{ aspectRatio: '16/9' }}>
        <video
          ref={videoRef}
          className="w-full h-full object-cover"
          playsInline
          muted
          autoPlay
        />
        
        {/* Scanning overlay */}
        {isScanning && (
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <div className="border-2 border-purple-500 bg-purple-500/10 rounded-lg animate-pulse" 
                 style={{ width: '70%', height: '40%' }}>
              <div className="w-full h-full border-2 border-dashed border-purple-300 rounded-lg" />
            </div>
          </div>
        )}
        
        {/* Status indicator */}
        <div className="absolute top-4 left-4">
          {isScanning ? (
            <div className="bg-green-600/80 text-white px-3 py-1 rounded-full text-sm flex items-center gap-2">
              <div className="w-2 h-2 bg-green-300 rounded-full animate-pulse"></div>
              Scanning
            </div>
          ) : (
            <div className="bg-yellow-600/80 text-white px-3 py-1 rounded-full text-sm">
              Starting...
            </div>
          )}
        </div>

        {/* Barcode detector status */}
        {!hasDetector && (
          <div className="absolute bottom-4 left-4 right-4">
            <div className="bg-orange-600/80 text-white px-3 py-1 rounded text-xs text-center">
              Manual scanning mode - automatic detection not supported
            </div>
          </div>
        )}
      </div>

      {/* Error state */}
      {error && (
        <div className="mt-4 bg-red-900/50 border border-red-700 text-red-300 px-4 py-3 rounded-lg">
          <p className="text-sm font-medium">Camera Error</p>
          <p className="text-xs mt-1">{error}</p>
          <button 
            onClick={startCamera}
            className="mt-2 bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs"
          >
            Retry Camera
          </button>
        </div>
      )}

      {/* Hidden canvas for barcode detection */}
      <canvas ref={canvasRef} className="hidden" />
    </div>
  )
}
